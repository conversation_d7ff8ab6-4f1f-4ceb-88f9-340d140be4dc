#include "resource_orchestrator.hpp"

// C++ standard library
#include <iostream>
#include <stdexcept>

// Local includes
#include "../../assets/assets_manager.hpp"
#include "../../assets/assets_loader.hpp"
#include "fallback_resources.hpp"

namespace IronFrost {
  ResourceOrchestrator::ResourceOrchestrator(AssetsManager& assetsManager, EventDispatcher& eventDispatcher)
    : m_assetsManager(assetsManager), m_eventDispatcher(eventDispatcher), m_unloaded(false) {}

  ResourceOrchestrator::~ResourceOrchestrator() {
    if (!m_unloaded) {
      unloadAllResources();
    }
  }

  void ResourceOrchestrator::loadAllResources(const IAssetsLoader& assetsLoader) {
    std::cout << "Loading resources through ResourceOrchestrator..." << std::endl;

    std::cout << "Loading renderer resources..." << std::endl;
    loadResources<ShaderData, LoadShaderEvent, ResourceType::SHADER>(assetsLoader);
    loadResources<PostprocessData, LoadPostprocessEffectEvent, ResourceType::POSTPROCESS_EFFECT>(assetsLoader);

    loadResources<ImageData, LoadTextureEvent, ResourceType::TEXTURE>(assetsLoader);
    loadResources<HeightmapData, LoadHeightmapEvent, ResourceType::HEIGHTMAP>(assetsLoader);
    loadResources<FontData, LoadFontEvent, ResourceType::FONT>(assetsLoader);
    loadResources<MaterialData, LoadMaterialEvent, ResourceType::MATERIAL>(assetsLoader);

    loadResources<MeshData, LoadMeshEvent, ResourceType::MESH>(assetsLoader);
    loadResources<ModelData, LoadModelEvent, ResourceType::MODEL>(assetsLoader);
    loadResources<TerrainData, LoadTerrainEvent, ResourceType::TERRAIN>(assetsLoader);

    std::cout << "Loading audio resources..." << std::endl;
    loadResources<AudioData, LoadAudioEvent, ResourceType::AUDIO>(assetsLoader);

    std::cout << "All resources loaded successfully" << std::endl;
  }

  void ResourceOrchestrator::loadEssentialResources() {
    std::cout << "Loading essential resources..." << std::endl;
    
    std::unique_ptr<MeshData> quadMeshData = m_assetsManager.createPrimitive(PrimitiveType::QUAD);
    m_eventDispatcher.dispatch<LoadMeshEvent>(FallbackResources::DEFAULT_QUAD_NAME, *quadMeshData);

    std::unique_ptr<MeshData> fallbackMeshData = m_assetsManager.createPrimitive(PrimitiveType::CUBE);
    m_eventDispatcher.dispatch<LoadMeshEvent>(FallbackResources::FALLBACK_MESH_NAME, *fallbackMeshData);
    m_eventDispatcher.dispatch<LoadMeshEvent>(FallbackResources::DEBUG_CUBE_NAME, *fallbackMeshData);

    std::unique_ptr<ImageData> fallbackImageData = m_assetsManager.loadImage("config/1x1-ffffffff.png");
    m_eventDispatcher.dispatch<LoadTextureEvent>(FallbackResources::FALLBACK_TEXTURE_NAME, *fallbackImageData);

    ModelData fallbackModelData;
    fallbackModelData.rootNode.meshes.emplace_back(std::move(fallbackMeshData));
    fallbackModelData.rootNode.textures.emplace_back(std::move(fallbackImageData));
    m_eventDispatcher.dispatch<LoadModelEvent>(FallbackResources::FALLBACK_MODEL_NAME, fallbackModelData);
    
    std::cout << "Essential resources loaded" << std::endl;
  }

  void ResourceOrchestrator::unloadAllResources() {
    if (m_unloaded) {
      return;
    }

    std::cout << "Unloading all resources..." << std::endl;

    m_unloaded = true;
    std::cout << "All resources unloaded" << std::endl;
  }
}
