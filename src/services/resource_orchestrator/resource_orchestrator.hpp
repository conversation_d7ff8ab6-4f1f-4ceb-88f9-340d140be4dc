#ifndef __IF__RESOURCE_ORCHESTRATOR_HPP
#define __IF__RESOURCE_ORCHESTRATOR_HPP

// C++ standard library
#include <memory>

// Local includes
#include "resource_type.hpp"
#include "resource_events.hpp"
#include "../../events/event_dispatcher.hpp"
#include "../../assets/assets_manager.hpp"
#include "../../assets/assets_loader.hpp"
#include "../service.hpp"

namespace IronFrost {
  // Forward declarations
  class IRenderer;
  class IAudioEngine;

  class ResourceOrchestrator : public IService {
    private:
      AssetsManager& m_assetsManager;
      EventDispatcher& m_eventDispatcher;

      bool m_unloaded;

    public:
      explicit ResourceOrchestrator(AssetsManager& assetsManager, EventDispatcher& eventDispatcher);
      ~ResourceOrchestrator();

      template <typename T, typename LoadEvent, ResourceType ResType>
      void loadResources(const AssetsLoader& assetsLoader, bool discardAfterLoad = true) {
        assetsLoader.load<T>(m_assetsManager,
          [&](const StringID name, const T& assetData) {
            std::cout << " + Loading resource: " << StringID::getString(name) << std::endl;
            m_eventDispatcher.dispatch<LoadEvent>(name, assetData, 
              [&name, &discardAfterLoad, &eventDispatcher = m_eventDispatcher, &assetsManager = m_assetsManager]() {
                if (discardAfterLoad) {
                  std::cout << " - Discarding resource: " << StringID::getString(name) << std::endl;
                  assetsManager.unload<T>(name);
                }

                eventDispatcher.dispatchAsync<ResourceCreatedEvent>(name, ResType);
              }
            );
          } 
        );
      }

      // Resource loading coordination
      void loadAllResources(const AssetsLoader& assetsLoader);
      void loadEssentialResources();
      void unloadAllResources();
  };
}

#endif
