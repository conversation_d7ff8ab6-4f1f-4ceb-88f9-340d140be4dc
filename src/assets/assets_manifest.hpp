#ifndef __IF__ASSETS_MANIFEST_HPP
#define __IF__ASSETS_MANIFEST_HPP

// C++ standard library
#include <string>
#include <unordered_map>
#include <variant>
#include <vector>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "../utils/string_id.hpp"
#include "../vfs/vfs.hpp"
#include "asset_types.hpp"

namespace IronFrost {
  class AssetsManifest {
    private:
      IVFS &m_vfs;

      json m_assetsConfig;

      // std::unordered_map<StringID, AssetInfo> m_assetsData;

      std::vector<AssetInfo> m_assetsData;

      template<AssetType T>
      void processAssets(const std::string& assetsType) {
        if (!m_assetsConfig.contains(assetsType)) return;

        auto assetsData = m_assetsConfig[assetsType];

        for (json::const_iterator it = assetsData.begin(); it != assetsData.end(); ++it) {
          m_assetsData.push_back(AssetInfo{T, it.value()});
        }
      }

      void parse() {
        processAssets<AssetType::MESH>("meshes");
        processAssets<AssetType::MODEL>("models");
        processAssets<AssetType::SHADER>("shaders");
        processAssets<AssetType::IMAGE>("textures");
        processAssets<AssetType::AUDIO>("audio");
        processAssets<AssetType::FONT>("fonts");
        processAssets<AssetType::MATERIAL>("materials");
        processAssets<AssetType::POSTPROCESS_EFFECT>("postprocess_effects");
        processAssets<AssetType::HEIGHTMAP>("heightmaps");
        processAssets<AssetType::TERRAIN>("terrains");
      }

    public:
      AssetsManifest(IVFS &vfs) : m_vfs(vfs) {}
      
      void loadFromFile(const std::string &path) {
        m_assetsConfig = json::parse(m_vfs.readFile(path));
        parse();
      }

      auto begin() const { return m_assetsData.begin(); }
      auto end() const { return m_assetsData.end(); }
      
      size_t size() const { return m_assetsData.size(); }

      AssetInfo takeNext() {
        auto it = m_assetsData.begin();
        auto asset = *it;

        m_assetsData.erase(it);

        return asset;
      };

      bool empty() const { return m_assetsData.empty(); }
  };
}

#endif
