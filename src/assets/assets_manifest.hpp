#ifndef __IF__ASSETS_MANIFEST_HPP
#define __IF__ASSETS_MANIFEST_HPP

// C++ standard library
#include <string>
#include <unordered_map>
#include <variant>
#include <vector>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "../utils/string_id.hpp"
#include "../vfs/vfs.hpp"


using json = nlohmann::json;

namespace IronFrost {
  enum class AssetType {
    MESH,
    MODEL,
    SHADER,
    IMAGE,
    AUDIO,
    FONT,
    MATERIAL,
    POSTPROCESS_EFFECT,
    HEIGHTMAP,
    TERRAIN, 
    UNKNOWN
  };

  struct AssetInfo {
    AssetType type{AssetType::UNKNOWN};
    json data;
  };

  class AssetsManifest {
    private:
      IVFS &m_vfs;

      json m_assetsConfig;

      std::unordered_map<StringID, AssetInfo> m_assetsData;

      template<AssetType T>
      void processAssets(const std::string& assetsType) {
        if (!m_assetsConfig.contains(assetsType)) return;

        auto assetsData = m_assetsConfig[assetsType];

        for (json::const_iterator it = assetsData.begin(); it != assetsData.end(); ++it) {
          StringID name = StringID(it.value()["name"]);

          m_assetsData[name] = AssetInfo{
            .type = T,
            .data = it.value()
          };

          std::cout << "Asset Manifest: " << StringID::getString(name) << " - " << static_cast<int>(T) << std::endl;
        }
      }

      void parse() {
        processAssets<AssetType::MESH>("meshes");
        processAssets<AssetType::MODEL>("models");
        processAssets<AssetType::SHADER>("shaders");
        processAssets<AssetType::IMAGE>("images");
        processAssets<AssetType::AUDIO>("audio");
        processAssets<AssetType::FONT>("fonts");
        processAssets<AssetType::MATERIAL>("materials");
        processAssets<AssetType::POSTPROCESS_EFFECT>("postprocess_effects");
        processAssets<AssetType::HEIGHTMAP>("heightmaps");
        processAssets<AssetType::TERRAIN>("terrains");
      }

    public:
      AssetsManifest(IVFS &vfs) : m_vfs(vfs) {}
      
      void loadFromFile(const std::string &path) {
        m_assetsConfig = json::parse(m_vfs.readFile(path));
        parse();
      }

      template<AssetType T>
      std::vector<StringID> getAssets() const {
        std::vector<StringID> assets;

        for (auto it = m_assetsData.begin(); it != m_assetsData.end(); ++it) {
          if (it->second.type == T) {
            assets.push_back(it->first);
          }
        }

        return assets;
      }

      const AssetInfo& getAsset(const StringID& name) const {
        return m_assetsData.at(name);
      }

      auto begin() const { return m_assetsData.begin(); }
      auto end() const { return m_assetsData.end(); }
      
      size_t size() const { return m_assetsData.size(); }

      AssetInfo takeNext() {
        auto it = m_assetsData.begin();
        auto asset = it->second;

        m_assetsData.erase(it);

        return asset;
      };

      bool empty() const { return m_assetsData.empty(); }
  };
}

#endif
