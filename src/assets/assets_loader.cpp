#include "assets_loader.hpp"

// C++ standard library
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

// Local includes
#include "../utils/string_id.hpp"
#include "../vfs/vfs.hpp"
#include "assets_manager.hpp"
#include "asset_types.hpp"
#include "asset_data_types.hpp"
#include "asset_events.hpp"
#include "loaders/image_loader.hpp"
#include "processors/image_atlas_processor.hpp"
#include "writers/image_writer.hpp"
#include "generators/default_texture_generator.hpp"

namespace IronFrost {

  const std::unordered_map<std::string, PrimitiveType> IAssetsLoader::primitiveTypes = {
    {"triangle", PrimitiveType::TRIANGLE},
    {"quad", PrimitiveType::QUAD},
    {"cube", PrimitiveType::CUBE},
    {"cone", PrimitiveType::CONE},
    {"cylinder", PrimitiveType::CYLINDER},
    {"sphere", PrimitiveType::SPHERE},
    {"plane", PrimitiveType::PLANE}
  };
  
  std::unique_ptr<IAssetsLoader> IAssetsLoader::fromFile(IVFS& vfs, const std::string& path) {
    return std::make_unique<AssetsFromFileLoader>(vfs, path);
  }

  BlinnPhongMaterialData AssetsFromFileLoader::loadBlinnPhongMaterial(AssetsManager& assetsManager, const json& material) const {
    BlinnPhongMaterialData blinnPhongMaterialData;

    blinnPhongMaterialData.ambient = material.contains("ambient") 
      ? glm::vec3{ material["ambient"][0], material["ambient"][1], material["ambient"][2] } 
      : glm::vec3(1.0F);

    blinnPhongMaterialData.diffuse = material.contains("diffuse")
      ? glm::vec3{ material["diffuse"][0], material["diffuse"][1], material["diffuse"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.specular = material.contains("specular")
      ? glm::vec3{ material["specular"][0], material["specular"][1], material["specular"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.shininess = material.contains("shininess")
      ? material["shininess"].get<float>()
      : 32.0F;

    blinnPhongMaterialData.textureName = StringID(material.contains("texture") ? material["texture"] : "");

    return blinnPhongMaterialData;
  }

  PBRMaterialData AssetsFromFileLoader::loadPBRMaterial(AssetsManager& assetsManager, const json& material) const {
    // BaseColor is required for PBR materials
    if (!material.contains("baseColor")) {
      throw std::runtime_error("PBR material must have a baseColor texture");
    }

    PBRMaterialData pbrMaterialData;
    pbrMaterialData.textureArray.reserve(6);

    // Load baseColor texture to determine reference size
    auto baseColorTexture = assetsManager.loadImage(material["baseColor"]);
    int width = baseColorTexture->width;
    int height = baseColorTexture->height;

    // Define texture types and their default generators
    struct TextureInfo {
      const char* key;
      std::function<std::unique_ptr<ImageData>(int, int)> defaultGenerator;
    };

    std::vector<TextureInfo> textureTypes = {
      {"baseColor", [](int w, int h) { return DefaultTextureGenerator::createBaseColorTexture(w, h); }},
      {"normal", [](int w, int h) { return DefaultTextureGenerator::createNormalTexture(w, h); }},
      {"metallic", [](int w, int h) { return DefaultTextureGenerator::createMetallicTexture(w, h); }},
      {"roughness", [](int w, int h) { return DefaultTextureGenerator::createRoughnessTexture(w, h); }},
      {"ao", [](int w, int h) { return DefaultTextureGenerator::createAOTexture(w, h); }},
      {"emissive", [](int w, int h) { return DefaultTextureGenerator::createEmissiveTexture(w, h); }}
    };

    // Add baseColor texture first
    pbrMaterialData.textureArray.emplace_back(std::move(baseColorTexture));

    // Load or generate remaining textures
    for (size_t i = 1; i < textureTypes.size(); i++) {
      const auto& textureType = textureTypes[i];

      if (material.contains(textureType.key)) {
        pbrMaterialData.textureArray.emplace_back(assetsManager.loadImage(material[textureType.key]));
      } else {
        pbrMaterialData.textureArray.emplace_back(textureType.defaultGenerator(width, height));
      }
    }

    return pbrMaterialData;
  }

  void AssetsFromFileLoader::processElement(const std::string& element, std::function<void(const json& element)> callback) const {
    if(!m_assetsConfig.contains(element)) {
      return;
    }

    auto elementConfig = m_assetsConfig[element];

    for (json::const_iterator it = elementConfig.begin(); it != elementConfig.end(); ++it) {
      callback(it.value());
    }
  }

  AssetsFromFileLoader::AssetsFromFileLoader(IVFS &vfs, const std::string &path) :
    m_vfs(vfs)
  {
    m_assetsConfig = json::parse(m_vfs.readFile(path));
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<MeshData> callback) const {
    return processElement("meshes", [&](const json& mesh) {
      StringID name = StringID(mesh["name"]);
      std::string type = mesh["type"];

      if(type == "primitive") {
        std::string primitive = mesh["primitive"];
        auto params = PrimitiveParams::fromJSON(mesh);
        callback(name, assetsManager.createPrimitive(name, primitiveTypes.at(primitive), params));
      }
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<ModelData> callback) const {
    return processElement("models", [&](const json& model) {
      StringID name = StringID(model["name"]);
      std::string path = model["path"];

      callback(name, assetsManager.loadModel(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<ShaderData> callback) const {
    return processElement("shaders", [&](const json& shader) {
      StringID name = StringID(shader["name"]);
      std::string path = shader["path"];

      callback(name, assetsManager.loadShader(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<ImageData> callback) const {
    return processElement("textures", [&](const json& texture) {
      StringID name = StringID(texture["name"]);
      std::string path = texture["path"];

      callback(name, assetsManager.loadImage(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<AudioData> callback) const {
    return processElement("audio", [&](const json& audio) {
      StringID name = StringID(audio["name"]);
      std::string path = audio["path"];

      callback(name, assetsManager.loadAudio(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<FontData> callback) const {
    return processElement("fonts", [&](const json& font) {
      StringID name = StringID(font["name"]);
      std::string path = font["path"];
      int size = font["size"];

      callback(name, assetsManager.loadFont(name, path, size));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<MaterialData> callback) const {
    return processElement("materials", [&](const json& material) {
      StringID name = StringID(material["name"]);
      std::string type = material["type"];

      if (type == "phong") {
        callback(name, MaterialData{loadBlinnPhongMaterial(assetsManager, material)});
      } else if (type == "pbr") {
        callback(name, MaterialData{loadPBRMaterial(assetsManager, material)});
      }
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<PostprocessData> callback) const {
    return processElement("postprocess", [&](const json& postprocess) {
      StringID name = StringID(postprocess["name"]);
      std::vector<std::string> shaderNames = postprocess["shaders"].get<std::vector<std::string>>();

      callback(name, PostprocessData{shaderNames});
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<HeightmapData> callback) const {
    return processElement("heightmaps", [&](const json& heightmap) {
      StringID name = StringID(heightmap["name"]);
      std::string path = heightmap["path"];

      callback(name, assetsManager.loadHeightmap(name, path));
    });
  }

  void AssetsFromFileLoader::loadAssets(AssetsManager& assetsManager, LoadCallback<TerrainData> callback) const {
    return processElement("terrains", [&](const json& terrain) {
      StringID name = StringID(terrain["name"]);
      std::string heightmapPath = terrain["heightmap"];
      float heightScale = terrain.value("heightScale", 1.0f);
      float blockSize = terrain.value("blockSize", 4.0f);

      callback(name, assetsManager.loadTerrain(name, TerrainParams{heightmapPath, heightScale, blockSize}));
    });
  }

  const std::unordered_map<std::string, PrimitiveType> AssetsLoader::s_primitiveTypes = {
    {"triangle", PrimitiveType::TRIANGLE},
    {"quad", PrimitiveType::QUAD},
    {"cube", PrimitiveType::CUBE},
    {"cone", PrimitiveType::CONE},
    {"cylinder", PrimitiveType::CYLINDER},
    {"sphere", PrimitiveType::SPHERE},
    {"plane", PrimitiveType::PLANE}
  };

  BlinnPhongMaterialData AssetsLoader::loadBlinnPhongMaterial(const json& materialConfig) const {
    BlinnPhongMaterialData blinnPhongMaterialData;

    blinnPhongMaterialData.ambient = materialConfig.contains("ambient") 
      ? glm::vec3{ materialConfig["ambient"][0], materialConfig["ambient"][1], materialConfig["ambient"][2] } 
      : glm::vec3(1.0F);

    blinnPhongMaterialData.diffuse = materialConfig.contains("diffuse")
      ? glm::vec3{ materialConfig["diffuse"][0], materialConfig["diffuse"][1], materialConfig["diffuse"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.specular = materialConfig.contains("specular")
      ? glm::vec3{ materialConfig["specular"][0], materialConfig["specular"][1], materialConfig["specular"][2] }
      : glm::vec3(1.0F);

    blinnPhongMaterialData.shininess = materialConfig.contains("shininess")
      ? materialConfig["shininess"].get<float>()
      : 32.0F;

    blinnPhongMaterialData.textureName = StringID(materialConfig.contains("texture") ? materialConfig["texture"] : "");

    return blinnPhongMaterialData;
  }

  PBRMaterialData AssetsLoader::loadPBRMaterial(const json& materialConfig) const {
    if (!materialConfig.contains("baseColor")) {
      throw std::runtime_error("PBR material must have a baseColor texture");
    }

    PBRMaterialData pbrMaterialData;
    pbrMaterialData.textureArray.reserve(6);

    auto baseColorTexture = m_assetsManager.loadImage(materialConfig["baseColor"]);
    int width = baseColorTexture->width;
    int height = baseColorTexture->height;

    struct TextureInfo {
      const char* key;
      std::function<std::unique_ptr<ImageData>(int, int)> defaultGenerator;
    };

    std::vector<TextureInfo> textureTypes = {
      {"baseColor", [](int w, int h) { return DefaultTextureGenerator::createBaseColorTexture(w, h); }},
      {"normal", [](int w, int h) { return DefaultTextureGenerator::createNormalTexture(w, h); }},
      {"metallic", [](int w, int h) { return DefaultTextureGenerator::createMetallicTexture(w, h); }},
      {"roughness", [](int w, int h) { return DefaultTextureGenerator::createRoughnessTexture(w, h); }},
      {"ao", [](int w, int h) { return DefaultTextureGenerator::createAOTexture(w, h); }},
      {"emissive", [](int w, int h) { return DefaultTextureGenerator::createEmissiveTexture(w, h); }}
    };

    pbrMaterialData.textureArray.emplace_back(std::move(baseColorTexture));

    for (size_t i = 1; i < textureTypes.size(); i++) {
      const auto& textureType = textureTypes[i];

      if (materialConfig.contains(textureType.key)) {
        pbrMaterialData.textureArray.emplace_back(m_assetsManager.loadImage(materialConfig[textureType.key]));
      } else {
        pbrMaterialData.textureArray.emplace_back(textureType.defaultGenerator(width, height));
      }
    }

    return pbrMaterialData;
  }

  template<>
  void AssetsLoader::load<AssetType::MESH>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string type = assetConfig["type"];

    if(type == "primitive") {
      std::string primitive = assetConfig["primitive"];
      auto params = PrimitiveParams::fromJSON(assetConfig);

      const MeshData& meshData = m_assetsManager.createPrimitive(name, s_primitiveTypes.at(primitive), params);
      m_eventDispatcher.dispatchAsync<LoadMeshEvent>(name, meshData);
    }
  };

  template<>
  void AssetsLoader::load<AssetType::MODEL>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const ModelData& modelData = m_assetsManager.loadModel(name, path);
    m_eventDispatcher.dispatchAsync<LoadModelEvent>(name, modelData);
  };

  template<>
  void AssetsLoader::load<AssetType::SHADER>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const ShaderData& shaderData = m_assetsManager.loadShader(name, path);
    m_eventDispatcher.dispatchAsync<LoadShaderEvent>(name, shaderData);
  };

  template<>
  void AssetsLoader::load<AssetType::IMAGE>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const ImageData& imageData = m_assetsManager.loadImage(name, path);
    m_eventDispatcher.dispatchAsync<LoadTextureEvent>(name, imageData);
  };

  template<>
  void AssetsLoader::load<AssetType::AUDIO>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const AudioData& audioData = m_assetsManager.loadAudio(name, path);
    m_eventDispatcher.dispatchAsync<LoadAudioEvent>(name, audioData);
  };

  template<>
  void AssetsLoader::load<AssetType::FONT>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];
    int size = assetConfig["size"];

    const FontData& fontData = m_assetsManager.loadFont(name, path, size);
    m_eventDispatcher.dispatchAsync<LoadFontEvent>(name, fontData);
  };
  
  template<>
  void AssetsLoader::load<AssetType::MATERIAL>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string type = assetConfig["type"];

    if (type == "phong") {
      const MaterialData& materialData = MaterialData{loadBlinnPhongMaterial(assetConfig)};
      m_eventDispatcher.dispatchAsync<LoadMaterialEvent>(name, materialData);
    } else if (type == "pbr") {
      const MaterialData& materialData = MaterialData{loadPBRMaterial(assetConfig)};
      m_eventDispatcher.dispatchAsync<LoadMaterialEvent>(name, materialData);
    }
  };

  template<>
  void AssetsLoader::load<AssetType::POSTPROCESS_EFFECT>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::vector<std::string> shaderNames = assetConfig["shaders"].get<std::vector<std::string>>();

    const PostprocessData& postprocessData = PostprocessData{shaderNames};
    m_eventDispatcher.dispatchAsync<LoadPostprocessEffectEvent>(name, postprocessData);
  }

  template<>
  void AssetsLoader::load<AssetType::HEIGHTMAP>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string path = assetConfig["path"];

    const HeightmapData& heightmapData = m_assetsManager.loadHeightmap(name, path);
    m_eventDispatcher.dispatchAsync<LoadHeightmapEvent>(name, heightmapData);
  }

  template<>
  void AssetsLoader::load<AssetType::TERRAIN>(const json& assetConfig) {
    StringID name = StringID(assetConfig["name"]);
    std::string heightmapPath = assetConfig["heightmap"];
    float heightScale = assetConfig.value("heightScale", 1.0f);
    float blockSize = assetConfig.value("blockSize", 4.0f);

    const TerrainData& terrainData = m_assetsManager.loadTerrain(name, TerrainParams{heightmapPath, heightScale, blockSize});
    m_eventDispatcher.dispatchAsync<LoadTerrainEvent>(name, terrainData);
  }

  const std::unordered_map<AssetType, AssetsLoader::AssetTypeFn, AssetsLoader::AssetTypeHash> AssetsLoader::s_dispatchMap = {
    {AssetType::MESH, &AssetsLoader::load<AssetType::MESH>},
    {AssetType::MODEL, &AssetsLoader::load<AssetType::MODEL>},
    {AssetType::SHADER, &AssetsLoader::load<AssetType::SHADER>},
    {AssetType::IMAGE, &AssetsLoader::load<AssetType::IMAGE>},
    {AssetType::AUDIO, &AssetsLoader::load<AssetType::AUDIO>},
    {AssetType::FONT, &AssetsLoader::load<AssetType::FONT>},
    {AssetType::MATERIAL, &AssetsLoader::load<AssetType::MATERIAL>},
    {AssetType::POSTPROCESS_EFFECT, &AssetsLoader::load<AssetType::POSTPROCESS_EFFECT>},
    {AssetType::HEIGHTMAP, &AssetsLoader::load<AssetType::HEIGHTMAP>},
    {AssetType::TERRAIN, &AssetsLoader::load<AssetType::TERRAIN>}
  };

  void AssetsLoader::load(const AssetInfo& assetInfo) {
    for (const auto& [type, fn] : s_dispatchMap) {
      if (type == assetInfo.type) {
        (this->*fn)(assetInfo.config);
        return;
      }
    }
    
    throw std::runtime_error("Unknown asset type: " + std::to_string(static_cast<int>(assetInfo.type)));
  }
}
