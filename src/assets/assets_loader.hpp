#ifndef __IF__ASSETS_LOADER_HPP
#define __IF__ASSETS_LOADER_HPP

// C++ standard library
#include <functional>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "asset_types.hpp"
#include "asset_data_types.hpp"
#include "asset_primitive_types.hpp"
#include "../services/service_locator.hpp"
#include "../events/event_dispatcher.hpp"

using json = nlohmann::json;

namespace IronFrost {
  class IVFS;
  class AssetsManager;
  class AssetsFromFileLoader;
  class StringID;

  template<typename T>
  using LoadCallback = std::function<void(const StringID, const T&)>;

  class IAssetsLoader {
    public:
      IAssetsLoader() = default;
      virtual ~IAssetsLoader() = default;

      static std::unique_ptr<IAssetsLoader> fromFile(IVFS& vfs, const std::string& path);

      template<typename T>
      void load(AssetsManager& manager, LoadCallback<T> callback) const {
        loadAssets(manager, callback);
      }

    protected:
      static const std::unordered_map<std::string, PrimitiveType> primitiveTypes;

      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<MeshData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<ModelData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<ShaderData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<ImageData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<AudioData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<FontData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<MaterialData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<PostprocessData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<HeightmapData> callback) const = 0;
      virtual void loadAssets(AssetsManager& assetsManager, LoadCallback<TerrainData> callback) const = 0;
  };

  class AssetsFromFileLoader : public IAssetsLoader {
    public:
      AssetsFromFileLoader(IVFS &vfs, const std::string &_path);

    private:
      IVFS &m_vfs;

      json m_assetsConfig;

      BlinnPhongMaterialData loadBlinnPhongMaterial(AssetsManager& assetsManager, const json& material) const;
      PBRMaterialData loadPBRMaterial(AssetsManager& assetsManager, const json& material) const;

      void processElement(const std::string& element, std::function<void(const json& element)> callback) const;

      void loadAssets(AssetsManager& assetsManager, LoadCallback<MeshData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<ModelData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<ShaderData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<ImageData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<AudioData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<FontData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<MaterialData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<PostprocessData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<HeightmapData> callback) const override;
      void loadAssets(AssetsManager& assetsManager, LoadCallback<TerrainData> callback) const override;

  };

  class AssetsLoader {
    private:
      AssetsManager& m_assetsManager;
      EventDispatcher& m_eventDispatcher;
      
      static const std::unordered_map<std::string, PrimitiveType> s_primitiveTypes;

      BlinnPhongMaterialData loadBlinnPhongMaterial(const json& materialConfig) const;
      PBRMaterialData loadPBRMaterial(const json& materialConfig) const;

      template<AssetType T>
      void load(const json& config);

      struct AssetTypeHash {
        size_t operator()(AssetType t) const noexcept {
          return static_cast<size_t>(t);
        }
      };
      
      using AssetTypeFn = void (AssetsLoader::*)(const json&);
      
      static const std::unordered_map<AssetType, AssetTypeFn, AssetTypeHash> s_dispatchMap;

    public:
      AssetsLoader(AssetsManager& assetsManager) : 
        m_assetsManager(assetsManager), m_eventDispatcher(ServiceLocator::getService<EventDispatcher>()) {}

      void load(const AssetInfo& assetInfo);
  };
}

#endif
